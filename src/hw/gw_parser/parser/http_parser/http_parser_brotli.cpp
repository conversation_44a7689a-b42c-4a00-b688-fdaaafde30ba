/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <memory.h>
#include <unistd.h>
#include <arpa/inet.h>

#include "http_parser.h"
#include "http_parser_common.h"
#include "http_filter.h"

#include "proto_http_parser.h"
#include "utils.h"

#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session_mgt.h"
#include "session.h"

#include "worker_queue.h"
#include "http_parser_brotli_task_worker.h"
#include "gw_stats.h"

#include "cJSON.h"
#include "simple_json.h"
#include "display_stats_define.h"
#include "get_file_type.h"

#define BROTLI_BODY_STRUCT_SIZE (128)
#define BROTLI_FILE_EXTENSION_LEN (256)

void CHttpParser::free_upload_brotli_req_info(http_req_info_t *p_brotli_req_info)
{
	BSTR_SAFE_FREE(p_brotli_req_info->p_body);
	BSTR_SAFE_FREE(p_brotli_req_info->p_header);
	BSTR_SAFE_FREE(p_brotli_req_info->p_req_header);
}

void CHttpParser::free_upload_brotli_rsp_info(http_rsp_info_t *p_brotli_rsp_info)
{
	BSTR_SAFE_FREE(p_brotli_rsp_info->p_header);
	BSTR_SAFE_FREE(p_brotli_rsp_info->p_rsp_header);
	SAFE_FREE(p_brotli_rsp_info->set_cookies_list);
}

void CHttpParser::free_upload_brotli_file_info(http_file_info_t *p_brotli_file_info)
{
	SAFE_FREE(p_brotli_file_info->p_file_type);
	SAFE_FREE(p_brotli_file_info->p_file_name);
}

void CHttpParser::free_upload_brotli_info(upload_http_info_t *p_upload_brotli_info)
{
	if (NULL == p_upload_brotli_info)
	{
		return;
	}

	free_upload_brotli_req_info(&(p_upload_brotli_info->http_req_info));
	free_upload_brotli_rsp_info(&(p_upload_brotli_info->http_rsp_info));
	free_upload_brotli_file_info(&(p_upload_brotli_info->http_file_info));

	free(p_upload_brotli_info);
}

CWorkerQueue *CHttpParser::new_wq_http_brotli(void)
{
	m_p_wq[HTTPPARSER_WQ_HTTP_BROTLI] = m_comm->create_worker_queue();
	CWorkerQueue *pwq = get_wq_http_brotli();
	if (pwq == NULL)
	{
		return NULL;
	}

	CTaskWorkerBrotli *ptw = new CTaskWorkerBrotli();
	ptw->set_parser(this);
	ptw->set_wq(pwq);
	m_p_tw[HTTPPARSER_WQ_HTTP_BROTLI] = ptw;

	pwq->set_gw_common(m_comm);
	pwq->set_watchdog(m_comm->get_watchdog());
	pwq->set_task_worker(ptw);

	pwq->set_queue_num_and_bytes(m_conf_http_brotli_queue_max_num, m_conf_http_brotli_queue_memory_max_size_bytes);
	pwq->set_queue_name(HTTP_BROTLI_QUEUE);
	pwq->init();
	pwq->create_queue();
	pwq->adjust_worker_thread_num(m_conf_http_brotli_thread_num);

	m_comm->get_gw_stats()->set_task(pwq->get_queue_name(), pwq, 52);
	m_comm->get_gw_stats()->set_qps(HTTP_BROTLI_QPS, &pwq->get_stats_task_data()->cnt);
	m_comm->get_gw_stats()->set_mem_stat(pwq->get_queue_name(), &pwq->get_queue_mem_size(), &pwq->get_queue_max_mem_size());

	return pwq;
}

CWorkerQueue *CHttpParser::new_wq_http_brotli_parser(void)
{
  m_p_wq[HTTPPARSER_WQ_HTTP_BROTLI_PARSER] = m_comm->create_worker_queue();
  CWorkerQueue *pwq = get_wq_http_brotli_parser();
  if (NULL == pwq)
  {
    return NULL;
  }
  CGwStats *pgws = m_comm->get_gw_stats();
  ASSERT(pgws != NULL);

  CTaskWorkerBrotliParser *ptw = new CTaskWorkerBrotliParser();
  ptw->set_parser(this);
  ptw->set_wq(pwq);
  m_p_tw[HTTPPARSER_WQ_HTTP_BROTLI_PARSER] = ptw;

  pwq->set_gw_common(m_comm);
  pwq->set_watchdog(m_comm->get_watchdog());
  pwq->set_task_worker(ptw);

  pwq->set_queue_num_and_bytes(m_conf_http_brotli_parser_queue_max_num, m_conf_http_brotli_parser_queue_memory_max_size_bytes);
  pwq->set_queue_name(HTTP_BROTLI_PARSER_QUEUE);
  pwq->init();
  pwq->create_queue();
  pwq->adjust_worker_thread_num(m_conf_http_brotli_parser_thread_num);

  pgws->set_task(pwq->get_queue_name(), pwq, 51);
  pgws->set_qps(HTTP_BROTLI_PARSER_QPS, &pwq->get_stats_task_data()->cnt);
  pgws->set_mem_stat(pwq->get_queue_name(), &pwq->get_queue_mem_size(), &pwq->get_queue_max_mem_size());

  return pwq;
}

void CHttpParser::http_cb_http_brotli(upload_http_info_t *p_upload_http_info, http_parser_ext_data_t *phped, int unkown_rule, int uploadflag, bool analyze)
{
  //int i_ret = 0;
  size_t mem_size = sizeof(http_brotli_t)
                  + phped->length
                  + sizeof(upload_http_info_t)
                  + sizeof(keyvalue_info_t) * 2
                  + p_upload_http_info->http_req_info.header_length
                  + p_upload_http_info->http_req_info.body.length
                  + p_upload_http_info->http_rsp_info.header_length;

  http_brotli_t *phb = new http_brotli_t;

  phb->length = phped->length;           // brotli uncompress data length
  phb->s = phped->pstr;                  // brotli uncompress data
  phb->deep = phped->brotli_deep;        // brotli deep
  phb->unkown_rule = unkown_rule;        // unkown rule
  phb->upload_flag = uploadflag;
  phb->p_upload_http_info = (upload_http_info_t*)malloc(sizeof(upload_http_info_t));
  memset(phb->p_upload_http_info, 0, sizeof(upload_http_info_t));
  memcpy(phb->p_upload_http_info, p_upload_http_info, sizeof(upload_http_info_t));
  phb->log_to_analyze=analyze;

  if (!get_wq_http_brotli()->queue_put_data(phb, mem_size))
  {
    goto failed;
  }

  return;

  failed:
    bstr_del_string(phb->s);
    free_upload_brotli_info(phb->p_upload_http_info);
    delete phb;
    return ;
}

void CHttpParser::free_http_brotli_inner(const http_brotli *p)
{
    bstr_del_string(p->s);

    if (!m_conf_http_brotli_parser_mode && p->p_upload_http_info && p->p_upload_http_info->is_file_event != 1)
    {
      free_upload_brotli_info(p->p_upload_http_info);
    }
}

int CHttpParser::worker_routine_http_brotli_inner(thread_local_brotli_data_t *ptlbd, const void *p, BrotliDecoderState *decoder_state)
{
  http_brotli_t *phb = (http_brotli_t *)p;
  const size_t buf_maxsize = ptlbd->buf_maxsize;
  const size_t body_buf_size = ptlbd->body_buf_size;
  size_t buf_size;
  char *body = ptlbd->body;
  char *body2 = ptlbd->body2;

  if (!(phb->s != NULL && (int)phb->length >= 0 && phb->p_upload_http_info != NULL))
  {
    /* 避免内存泄漏 */
    if (m_conf_http_brotli_parser_mode)
    {
      free_upload_brotli_info(phb->p_upload_http_info);
    }
    return 0;
  }

  char *buf = phb->s;
  buf_size = 0;

  // 执行解压操作并将结果放到 事件消息上传
  if (phb->length > 0)
  {
    int brotli_st;
    int brotli_deep = phb->deep;
    char *buf_src = phb->s;
    size_t len_src = phb->length;
    char *buf_dst = body;
    char *pp;

    // brotli 支持嵌套解压，解压嵌套的深度
    while (brotli_deep >= 1)
    {
        brotli_st = http_brotli_routine(brotli_deep, &buf_size, buf_dst, body_buf_size, buf_maxsize, buf_src, len_src, decoder_state);

        buf = buf_dst;
        if (brotli_st)
        {
          if (buf_size > 0)
          {
            // 截断
            if (brotli_deep == 1)
            {
              // 最后一层，仍使用解压后的数据
              break;
            }
          }
          else
          {
            // 异常退出
            buf_size = 0;
            break;
          }
        }

        if (brotli_deep == 1)
        {
          // 最后一层无需交换
          break;
        }
        if (buf_src == phb->s)
        {
          buf_src = body;
          buf_dst = body2;
        }
        else
        {
          pp = buf_src;
          buf_src = buf_dst;
          buf_dst = pp;
        }
        len_src = buf_size;

        brotli_deep--;
    }
  }
  else if (phb->s != NULL && phb->length == 0)
  {
    get_wq_http_brotli()->status_count(WQ_STATUS_FAIL, 1);
  }

  if ((int)buf_size >= 0)
  {
    if (m_conf_http_brotli_parser_mode)
    {
      http_cb_http_brotli_parser(phb, buf, buf_size);
    }
    else
    {
      http_brotli_upload_msg(phb->p_upload_http_info, phb->upload_flag, phb->unkown_rule, phb->log_to_analyze, buf, buf_size);
    }
  }
  return 1;
}

int CHttpParser::http_brotli_routine(int brotli_deep, size_t *p_buf_size_out, char *body, size_t body_buf_size, const size_t buf_maxsize, char *pstr, size_t length, BrotliDecoderState *decoder_state)
{
  size_t maxsize;
  size_t buf_size = 0;
  int ret = 0;

  maxsize = MIN(buf_maxsize, (size_t)m_conf_http_response_body_max_size);

  // 确保decoder_state有效
  if (!decoder_state)
  {
    // 解压失败
    get_wq_http_brotli()->status_count(WQ_STATUS_FAIL2, 1);
    get_wq_http_brotli()->status_count(WQ_STATUS_FAIL, 1);
    buf_size = 0;
    ret = 1;
    *p_buf_size_out = buf_size;
    return ret;
  }

  // Perform Brotli decompression
  const uint8_t *input_ptr = reinterpret_cast<const uint8_t*>(pstr);
  size_t available_in = length;
  uint8_t *output_ptr = reinterpret_cast<uint8_t*>(body);
  size_t available_out = body_buf_size;
  size_t total_out = 0;
  size_t initial_available_out = available_out;

  BrotliDecoderResult result;

  do {
    result = BrotliDecoderDecompressStream(decoder_state,
                                          &available_in, &input_ptr,
                                          &available_out, &output_ptr,
                                          &total_out);

    if (result == BROTLI_DECODER_RESULT_ERROR) {
      // 解压失败
      get_wq_http_brotli()->status_count(WQ_STATUS_FAIL2, 1);
      get_wq_http_brotli()->status_count(WQ_STATUS_FAIL, 1);
      buf_size = 0;
      ret = 1;
      break;
    }

    if (result == BROTLI_DECODER_RESULT_NEEDS_MORE_OUTPUT) {
      // 计算当前已解压的字节数
      size_t current_output = initial_available_out - available_out;
      buf_size = current_output;

      if (current_output >= maxsize) {
        // 解压数据过长
        get_wq_http_brotli()->status_count(WQ_STATUS_FAIL4, 1);
        get_wq_http_brotli()->status_count(WQ_STATUS_FAIL, 1);
        ret = 2;
        break;
      }
      // 输出缓冲区已满但还有数据要解压
      get_wq_http_brotli()->status_count(WQ_STATUS_FAIL4, 1);
      get_wq_http_brotli()->status_count(WQ_STATUS_FAIL, 1);
      ret = 2;
      break;
    }

  } while (result == BROTLI_DECODER_RESULT_NEEDS_MORE_INPUT && available_in > 0);

  if (result == BROTLI_DECODER_RESULT_SUCCESS) {
    // 计算实际解压的字节数
    buf_size = initial_available_out - available_out;

    if (buf_size > maxsize) {
      // 解压数据过长
      get_wq_http_brotli()->status_count(WQ_STATUS_FAIL4, 1);
      get_wq_http_brotli()->status_count(WQ_STATUS_FAIL, 1);
      ret = 2;
    } else {
      if (brotli_deep == 1) {
        // 解压到最后一层，可认为解成功
        get_wq_http_brotli()->status_count(WQ_STATUS_SUCC, 1);
      }
    }
  }

  *p_buf_size_out = buf_size;

  return ret;
}

void CHttpParser::http_cb_http_brotli_parser(http_brotli_t *phb, char *s, size_t length)
{
  char *p_brotli_data = NULL;
  http_brotli_parser_t *phbp = NULL;
  size_t mem_size = 0;

  if (length == 0)
  {
    if (phb->length == 0)
    {
        goto end;
    }
    else
    {
        s = phb->s;
        length = phb->length;
    }
  }

  p_brotli_data = (char *)malloc(length + 1);
  if (NULL == p_brotli_data)
  {
    goto end;
  }

  memcpy(p_brotli_data, s, length);
  p_brotli_data[length] = '\0';
  mem_size = sizeof(http_brotli_parser_t) + length;
  phbp = new http_brotli_parser_t();
  phbp->p_upload_http_info = phb->p_upload_http_info;
  phbp->unknown_rule = phb->unkown_rule;
  phbp->upload_flag = phb->upload_flag;
  phbp->log_to_analyze = phb->log_to_analyze;
  phbp->length = length;
  phbp->s = p_brotli_data;

  if (!get_wq_http_brotli_parser()->queue_put_data(phbp, mem_size))
  {
    free_http_brotli_parser_data(phbp);
    return;
  }

  return;

end:
  get_wq_http_brotli_parser()->status_count(WQ_QUEUE_FAIL, 1);   /* 记录入队列失败 */
  SAFE_FREE(p_brotli_data);
  free_upload_brotli_info(phb->p_upload_http_info);

  return;
}

void CHttpParser::free_http_brotli_parser_data(const http_brotli_parser *phbp)
{
  if (!phbp)
  {
    return;
  }

  if (phbp->p_upload_http_info && phbp->p_upload_http_info->is_file_event == 1)
  {
      delete phbp;
      return;
  }

  if (phbp->p_upload_http_info && phbp->p_upload_http_info->is_file_event != 1)
  {
    free_upload_brotli_info(phbp->p_upload_http_info);
  }

  SAFE_FREE(phbp->s);

  delete phbp;
}

int CHttpParser::http_brotli_parser_routine(const void* p)
{
  http_brotli_parser_t *phbp = (http_brotli_parser_t*)p;

  return http_brotli_upload_msg(phbp->p_upload_http_info, phbp->upload_flag, phbp->unknown_rule, phbp->log_to_analyze, phbp->s, phbp->length);
}

int CHttpParser::http_brotli_upload_msg(upload_http_info *p_upload_brotli_info, int upload_flag, int unknown_rule, bool log_to_analyze, char *body, size_t buf_size)
{
	char *ss = NULL;
	size_t ss_len = 0;
	const char *p_req_url = NULL;
	char *p_content_type = NULL;
	char *p_content_disposition = NULL;
	char *p_content_range = NULL;
	char *p_file_name = NULL;
	char a_body_struct[BROTLI_BODY_STRUCT_SIZE];
	memset(a_body_struct, 0, BROTLI_BODY_STRUCT_SIZE);
	int i_body_len = 0;
	i_body_len = MIN(BROTLI_BODY_STRUCT_SIZE, buf_size);
	memcpy(a_body_struct, body, i_body_len);
	size_t file_len = 0;

	// upload_flag默认为0
	if (upload_flag == 0)
	{
		p_req_url = p_upload_brotli_info->http_req_info.full_url;
		/* 获取request url字段，response content-type、content-disposition字段 */
		get_http_gzip_info(&(p_upload_brotli_info->http_rsp_info), p_req_url, &p_content_type, &p_content_disposition, &p_content_range, &file_len, &p_file_name);
		file_len = buf_size;

		bstring st_body;
		st_body.bstr = body;
		st_body.length = buf_size;
		char mime[256] = {0};

		if (p_content_type && strlen(p_content_type) > 0)
		{
			memcpy(mime, p_content_type, MIN(strlen(p_content_type),255));
		}
		probe_file_event(&st_body, mime, p_content_disposition, p_upload_brotli_info);

		if (p_upload_brotli_info->is_file_event == 1)
		{
			p_upload_brotli_info->http_file_info.p_file_direction = "download";
			p_upload_brotli_info->http_file_info.file_len = st_body.length;
			p_upload_brotli_info->rw_flag = 0; // RW_FLAG_READ
			get_http_file_type(&p_upload_brotli_info->http_file_info, mime);
			get_file_name(&p_upload_brotli_info->http_file_info, p_content_disposition, p_upload_brotli_info->http_req_info.full_url);
		}

		if (p_upload_brotli_info->http_file_info.file_len >= (size_t)m_conf_http_response_body_max_size)
		{
			p_upload_brotli_info->http_file_info.p_file_warn = g_p_file_warn;
		}

		free_param(p_content_type, p_content_disposition, p_content_range, p_file_name);
	}

	p_upload_brotli_info->http_rsp_info.body.p_value = body;
	p_upload_brotli_info->http_rsp_info.body.length = buf_size;

	char s[1024] = "\0";
	ss = simple_json_encode(*p_upload_brotli_info, unknown_rule, log_to_analyze, &ss_len, s);
	if (ss != NULL)
	{
		if (1 == p_upload_brotli_info->is_file_event)
		{
			unknown_rule = 2;
		}

		http_cb_upload_msg(ss, unknown_rule, log_to_analyze, ss_len, s);
	}

	return 0;
}
